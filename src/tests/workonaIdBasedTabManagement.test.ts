/**
 * Workona ID血缘关系标签页管理测试
 * 验证基于Workona ID的标签页添加功能
 */

import { WorkspaceManager } from '../utils/workspace';
import { WorkonaTabManager } from '../utils/workonaTabManager';
import { StorageManager } from '../utils/storage';

// Mock Chrome APIs
const mockChrome = {
  tabs: {
    query: jest.fn(),
    create: jest.fn(),
    get: jest.fn(),
  },
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
    },
  },
};

// @ts-ignore
global.chrome = mockChrome;

describe('Workona ID血缘关系标签页管理', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addCurrentTabByWorkonaId', () => {
    it('应该为没有Workona ID的标签页创建新的工作区核心映射', async () => {
      // 模拟工作区数据
      const mockWorkspace = {
        id: 'test-workspace',
        name: 'Test Workspace',
        websites: [],
        updatedAt: Date.now(),
      };

      // 模拟存储返回
      jest.spyOn(StorageManager, 'getWorkspaces').mockResolvedValue({
        success: true,
        data: [mockWorkspace],
      });

      jest.spyOn(StorageManager, 'saveWorkspaces').mockResolvedValue({
        success: true,
      });

      // 模拟标签页没有Workona ID
      jest.spyOn(WorkonaTabManager, 'getWorkonaIdByChromeId').mockResolvedValue({
        success: false,
        data: null,
      });

      // 模拟创建Workona ID映射
      jest.spyOn(WorkonaTabManager, 'createTabIdMapping').mockResolvedValue({
        success: true,
        data: {
          workonaId: 't-test-workspace-12345',
          chromeId: 123,
          workspaceId: 'test-workspace',
          isWorkspaceCore: true,
          tabType: 'core',
        } as any,
      });

      // 模拟生成Workona ID
      jest.spyOn(WorkonaTabManager, 'generateWorkonaTabId').mockReturnValue('t-test-workspace-12345');

      const result = await WorkspaceManager.addCurrentTabByWorkonaId(
        'test-workspace',
        123,
        {
          url: 'https://example.com',
          title: 'Example Site',
          favicon: 'https://example.com/favicon.ico',
        }
      );

      expect(result.success).toBe(true);
      expect(result.data?.url).toBe('https://example.com');
      expect(WorkonaTabManager.createTabIdMapping).toHaveBeenCalledWith(
        't-test-workspace-12345',
        123,
        'test-workspace',
        expect.any(String),
        {
          isWorkspaceCore: true,
          source: 'workspace_website',
        }
      );
    });

    it('应该提升现有的会话临时标签页为工作区核心', async () => {
      // 模拟工作区数据
      const mockWorkspace = {
        id: 'test-workspace',
        name: 'Test Workspace',
        websites: [],
        updatedAt: Date.now(),
      };

      jest.spyOn(StorageManager, 'getWorkspaces').mockResolvedValue({
        success: true,
        data: [mockWorkspace],
      });

      jest.spyOn(StorageManager, 'saveWorkspaces').mockResolvedValue({
        success: true,
      });

      // 模拟标签页已有Workona ID
      jest.spyOn(WorkonaTabManager, 'getWorkonaIdByChromeId').mockResolvedValue({
        success: true,
        data: 't-test-workspace-existing',
      });

      // 模拟标签页元数据（会话临时标签页）
      jest.spyOn(WorkonaTabManager, 'getTabMetadata').mockResolvedValue({
        success: true,
        data: {
          isWorkspaceCore: false,
          tabType: 'session',
        } as any,
      });

      // 模拟提升为工作区核心
      jest.spyOn(WorkonaTabManager, 'promoteToWorkspaceCore').mockResolvedValue({
        success: true,
        data: {} as any,
      });

      const result = await WorkspaceManager.addCurrentTabByWorkonaId(
        'test-workspace',
        123,
        {
          url: 'https://example.com',
          title: 'Example Site',
        }
      );

      expect(result.success).toBe(true);
      expect(WorkonaTabManager.promoteToWorkspaceCore).toHaveBeenCalledWith(
        't-test-workspace-existing',
        expect.any(String)
      );
    });

    it('应该处理重复URL的情况', async () => {
      // 模拟工作区已有相同URL的网站
      const mockWorkspace = {
        id: 'test-workspace',
        name: 'Test Workspace',
        websites: [
          {
            id: 'existing-website',
            url: 'https://example.com',
            title: 'Existing Site',
          },
        ],
        updatedAt: Date.now(),
      };

      jest.spyOn(StorageManager, 'getWorkspaces').mockResolvedValue({
        success: true,
        data: [mockWorkspace],
      });

      const result = await WorkspaceManager.addCurrentTabByWorkonaId(
        'test-workspace',
        123,
        {
          url: 'https://example.com',
          title: 'Example Site',
        }
      );

      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('工作区中已存在使用该网址的网站');
    });
  });
});
