import React, { useState } from 'react';
import { X, Plus, ExternalLink } from 'lucide-react';
import { URL_REGEX } from '@/utils/constants';

interface AddWebsiteModalProps {
  onClose: () => void;
  onAdd: (url: string) => void;
}

/**
 * 添加网站模态框
 */
const AddWebsiteModal: React.FC<AddWebsiteModalProps> = ({
  onClose,
  onAdd,
}) => {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');

  /**
   * 验证URL格式
   */
  const validateUrl = (inputUrl: string): boolean => {
    if (!inputUrl.trim()) {
      setError('请输入网站URL');
      return false;
    }

    // 如果没有协议，自动添加https://
    let formattedUrl = inputUrl.trim();
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      formattedUrl = 'https://' + formattedUrl;
    }

    if (!URL_REGEX.test(formattedUrl)) {
      setError('请输入有效的网站URL');
      return false;
    }

    setError('');
    setUrl(formattedUrl);
    return true;
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateUrl(url)) {
      onAdd(url);
    }
  };

  /**
   * 处理URL输入变化
   */
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputUrl = e.target.value;
    setUrl(inputUrl);
    
    // 清除错误信息
    if (error) {
      setError('');
    }
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  /**
   * 获取当前标签页URL
   */
  const handleGetCurrentTab = async () => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0 && tabs[0].url) {
        const currentUrl = tabs[0].url;
        if (!currentUrl.startsWith('chrome://')) {
          setUrl(currentUrl);
          setError('');
        }
      }
    } catch (error) {
      console.error('Failed to get current tab:', error);
    }
  };



  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            添加网站
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit}>
          {/* URL输入 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              网站URL
            </label>
            <div className="relative">
              <input
                type="text"
                value={url}
                onChange={handleUrlChange}
                placeholder="输入网站URL，例如：google.com"
                className={`input-field w-full pr-10 ${error ? 'border-red-500' : ''}`}
                autoFocus
              />
              <ExternalLink className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            </div>
            {error && (
              <p className="text-red-400 text-sm mt-1">{error}</p>
            )}
          </div>

          {/* 快速操作 */}
          <div className="mb-4">
            <button
              type="button"
              onClick={handleGetCurrentTab}
              className="text-sm text-blue-400 hover:text-blue-300 flex items-center gap-1"
            >
              <ExternalLink className="w-3 h-3" />
              使用当前标签页URL
            </button>
          </div>



          {/* 按钮 */}
          <div className="flex gap-3 justify-end">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!url.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Plus className="w-4 h-4" />
              添加网站
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddWebsiteModal;
