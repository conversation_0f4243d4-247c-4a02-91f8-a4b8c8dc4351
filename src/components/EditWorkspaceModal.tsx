import React, { useState } from 'react';
import { X, Save } from 'lucide-react';
import { WorkSpace } from '@/types/workspace';
import { WORKSPACE_COLORS, WORKSPACE_ICONS } from '@/utils/constants';

interface EditWorkspaceModalProps {
  workspace: WorkSpace;
  onClose: () => void;
  onSave: (updates: { name?: string; icon?: string; color?: string }) => void;
}

/**
 * 编辑工作区模态框
 */
const EditWorkspaceModal: React.FC<EditWorkspaceModalProps> = ({
  workspace,
  onClose,
  onSave,
}) => {
  const [name, setName] = useState(workspace.name);
  const [selectedIcon, setSelectedIcon] = useState(workspace.icon);
  const [selectedColor, setSelectedColor] = useState(workspace.color);

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      return;
    }

    const updates: { name?: string; icon?: string; color?: string } = {};
    
    if (name.trim() !== workspace.name) {
      updates.name = name.trim();
    }
    
    if (selectedIcon !== workspace.icon) {
      updates.icon = selectedIcon;
    }
    
    if (selectedColor !== workspace.color) {
      updates.color = selectedColor;
    }

    onSave(updates);
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  /**
   * 检查是否有更改
   */
  const hasChanges = 
    name.trim() !== workspace.name ||
    selectedIcon !== workspace.icon ||
    selectedColor !== workspace.color;

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            编辑工作区
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit}>
          {/* 工作区名称 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              工作区名称
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入工作区名称..."
              className="input-field w-full"
              autoFocus
            />
          </div>

          {/* 图标选择 - 优化360px宽度 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择图标
            </label>
            <div className="grid grid-cols-6 gap-1.5 max-h-20 overflow-y-auto">
              {WORKSPACE_ICONS.map((icon) => (
                <button
                  key={icon}
                  type="button"
                  onClick={() => setSelectedIcon(icon)}
                  className={`p-1.5 rounded text-base transition-colors duration-200 ${
                    selectedIcon === icon
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
          </div>

          {/* 颜色选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择颜色
            </label>
            <div className="flex gap-2 flex-wrap">
              {WORKSPACE_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setSelectedColor(color)}
                  className={`w-8 h-8 rounded-lg transition-all duration-200 ${
                    selectedColor === color
                      ? 'ring-2 ring-white ring-offset-2 ring-offset-slate-800'
                      : 'hover:scale-110'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* 预览 */}
          <div className="mb-6 p-3 bg-slate-700 rounded-lg">
            <div className="text-sm text-slate-200 mb-2">预览</div>
            <div className="flex items-center gap-3">
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
                style={{ backgroundColor: selectedColor + '20', color: selectedColor }}
              >
                {selectedIcon}
              </div>
              <span className="text-white font-medium">
                {name || '工作区名称'}
              </span>
            </div>
          </div>

          {/* 按钮 */}
          <div className="flex gap-3 justify-end">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!name.trim() || !hasChanges}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4" />
              保存更改
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditWorkspaceModal;
