import { ERROR_CODES } from './constants';

/**
 * 错误处理工具类
 * 提供统一的错误消息处理和中文化
 */
export class ErrorHandler {
  /**
   * 错误消息映射表（中文）
   */
  private static readonly ERROR_MESSAGES: Record<string, string> = {
    // 工作区相关错误
    [ERROR_CODES.WORKSPACE_NOT_FOUND]: '工作区不存在',
    [ERROR_CODES.DUPLICATE_WORKSPACE]: '工作区名称已存在',
    
    // 网站相关错误
    [ERROR_CODES.WEBSITE_NOT_FOUND]: '网站不存在',
    [ERROR_CODES.DUPLICATE_WEBSITE]: '工作区中已存在使用该网址的网站',
    
    // 存储相关错误
    [ERROR_CODES.STORAGE_ERROR]: '数据存储失败',
    
    // 标签页相关错误
    [ERROR_CODES.TAB_ERROR]: '标签页操作失败',
    
    // 窗口相关错误
    [ERROR_CODES.WINDOW_ERROR]: '窗口操作失败',
    
    // 权限相关错误
    [ERROR_CODES.PERMISSION_DENIED]: '权限不足',
    
    // URL相关错误
    [ERROR_CODES.INVALID_URL]: '无效的网址格式',
  };

  /**
   * 获取中文错误消息
   */
  static getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.message) {
      // 如果已经是中文消息，直接返回
      if (this.isChineseMessage(error.message)) {
        return error.message;
      }

      // 尝试从错误代码映射获取中文消息
      if (error.code && this.ERROR_MESSAGES[error.code]) {
        return this.ERROR_MESSAGES[error.code];
      }

      // 翻译常见的英文错误消息
      return this.translateCommonErrors(error.message);
    }

    if (error?.error?.message) {
      return this.getErrorMessage(error.error);
    }

    if (error?.code && this.ERROR_MESSAGES[error.code]) {
      return this.ERROR_MESSAGES[error.code];
    }

    return '操作失败，请重试';
  }

  /**
   * 检查是否为中文消息
   */
  private static isChineseMessage(message: string): boolean {
    return /[\u4e00-\u9fa5]/.test(message);
  }

  /**
   * 翻译常见的英文错误消息
   */
  private static translateCommonErrors(message: string): string {
    const translations: Record<string, string> = {
      'Workspace name cannot be empty': '工作区名称不能为空',
      'Workspace with this name already exists': '工作区名称已存在',
      'Failed to create workspace': '创建工作区失败',
      'Failed to update workspace': '更新工作区失败',
      'Failed to delete workspace': '删除工作区失败',
      'Failed to add website': '添加网站失败',
      'Failed to update website': '更新网站失败',
      'Failed to delete website': '删除网站失败',
      'Failed to switch workspace': '切换工作区失败',
      'Failed to export data': '导出数据失败',
      'Failed to import data': '导入数据失败',
      'Failed to clear storage': '清除数据失败',
      'Invalid data format': '数据格式无效',
      'Invalid URL format': '网址格式无效',
      'Permission denied': '权限不足',
      'Network error': '网络错误',
      'Unknown error': '未知错误',
      'Tab not found': '标签页不存在',
      'Window not found': '窗口不存在',
      'Extension context invalidated': '扩展上下文已失效，请刷新页面',
    };

    // 精确匹配
    if (translations[message]) {
      return translations[message];
    }

    // 模糊匹配
    for (const [english, chinese] of Object.entries(translations)) {
      if (message.toLowerCase().includes(english.toLowerCase())) {
        return chinese;
      }
    }

    // 如果没有匹配的翻译，返回原消息
    return message;
  }

  /**
   * 格式化错误消息用于显示
   */
  static formatErrorForDisplay(error: any, context?: string): string {
    const baseMessage = this.getErrorMessage(error);
    
    if (context) {
      return `${context}：${baseMessage}`;
    }
    
    return baseMessage;
  }

  /**
   * 检查是否为网络错误
   */
  static isNetworkError(error: any): boolean {
    const message = error?.message || error || '';
    return message.toLowerCase().includes('network') || 
           message.toLowerCase().includes('fetch') ||
           message.toLowerCase().includes('connection');
  }

  /**
   * 检查是否为权限错误
   */
  static isPermissionError(error: any): boolean {
    const code = error?.code || error?.error?.code;
    return code === ERROR_CODES.PERMISSION_DENIED ||
           (error?.message || '').toLowerCase().includes('permission');
  }

  /**
   * 检查是否为重复错误
   */
  static isDuplicateError(error: any): boolean {
    const code = error?.code || error?.error?.code;
    return code === ERROR_CODES.DUPLICATE_WORKSPACE ||
           code === ERROR_CODES.DUPLICATE_WEBSITE;
  }

  /**
   * 获取错误的严重程度
   */
  static getErrorSeverity(error: any): 'low' | 'medium' | 'high' {
    if (this.isPermissionError(error)) {
      return 'high';
    }
    
    if (this.isNetworkError(error)) {
      return 'medium';
    }
    
    if (this.isDuplicateError(error)) {
      return 'low';
    }
    
    return 'medium';
  }
}

/**
 * Toast 错误处理器
 * 用于在组件中显示错误消息
 */
export class ToastErrorHandler {
  private showError: (message: string, duration?: number) => void;

  constructor(showError: (message: string, duration?: number) => void) {
    this.showError = showError;
  }

  /**
   * 处理并显示错误
   */
  handle(error: any, context?: string): void {
    const message = ErrorHandler.formatErrorForDisplay(error, context);
    const severity = ErrorHandler.getErrorSeverity(error);
    
    // 根据严重程度调整显示时间
    const duration = severity === 'high' ? 8000 : severity === 'medium' ? 6000 : 4000;
    
    this.showError(message, duration);
  }

  /**
   * 处理工作区操作错误
   */
  handleWorkspaceError(error: any, operation: string): void {
    const context = `${operation}工作区`;
    this.handle(error, context);
  }

  /**
   * 处理网站操作错误
   */
  handleWebsiteError(error: any, operation: string): void {
    const context = `${operation}网站`;
    this.handle(error, context);
  }

  /**
   * 处理数据操作错误
   */
  handleDataError(error: any, operation: string): void {
    const context = `${operation}数据`;
    this.handle(error, context);
  }
}
