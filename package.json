{"name": "workspace-pro-chrome-extension", "version": "1.0.0", "description": "Professional Chrome workspace management extension for AI tools and tech workers", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node scripts/verify-build.js", "build-only": "vite build", "build-with-types": "tsc && vite build && node scripts/verify-build.js", "preview": "vite preview", "type-check": "tsc --noEmit", "verify": "node scripts/verify-build.js"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/chrome": "^0.0.251", "@types/node": "^24.0.10", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "keywords": ["chrome-extension", "workspace", "tabs", "productivity", "ai-tools"], "author": "WorkSpace Pro Team", "license": "MIT"}