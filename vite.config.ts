import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { copyFileSync, existsSync, mkdirSync } from 'fs'
import path from 'path'

// 自定义插件：复制workspace-placeholder文件
const copyWorkspacePlaceholderPlugin = () => {
  return {
    name: 'copy-workspace-placeholder',
    writeBundle() {
      // 在写入bundle完成后复制文件
      try {
        console.log('🔄 复制workspace-placeholder文件...')

        // 确保dist目录存在
        if (!existsSync('dist')) {
          mkdirSync('dist', { recursive: true })
        }

        // 复制文件到dist根目录
        copyFileSync('public/manifest.json', 'dist/manifest.json')
        copyFileSync('public/workspace-placeholder.html', 'dist/workspace-placeholder.html')
        copyFileSync('public/workspace-placeholder.js', 'dist/workspace-placeholder.js')

        // 复制icons目录
        if (!existsSync('dist/icons')) {
          mkdirSync('dist/icons', { recursive: true })
        }
        copyFileSync('public/icons/icon16.png', 'dist/icons/icon16.png')
        copyFileSync('public/icons/icon32.png', 'dist/icons/icon32.png')
        copyFileSync('public/icons/icon48.png', 'dist/icons/icon48.png')
        copyFileSync('public/icons/icon128.png', 'dist/icons/icon128.png')

        console.log('✅ workspace-placeholder文件复制完成')
      } catch (error) {
        console.error('❌ 复制workspace-placeholder文件失败:', error)
      }
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), copyWorkspacePlaceholderPlugin()],
  resolve: {
    alias: {
      '@': path.resolve(process.cwd(), 'src'),
    },
  },
  build: {
    outDir: 'dist',
    rollupOptions: {
      input: {
        sidepanel: 'sidepanel.html',
        background: 'src/background/background.ts',
      },
      output: {
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'background') {
            return 'background.js'
          }
          return 'assets/[name]-[hash].js'
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    target: 'esnext',
    minify: false,
    copyPublicDir: false, // 禁用自动复制public目录
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
})
