#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 验证构建结果...');

const distDir = path.join(path.dirname(__dirname), 'dist');
const requiredFiles = [
  'manifest.json',
  'background.js',
  'sidepanel.html',
  'workspace-placeholder.html',
  'workspace-placeholder.js'
];

const requiredIcons = [
  'icons/icon16.png',
  'icons/icon32.png',
  'icons/icon48.png',
  'icons/icon128.png'
];

let allFilesExist = true;

console.log('\n📁 检查必需文件:');
requiredFiles.forEach(file => {
  const filePath = path.join(distDir, file);
  const exists = fs.existsSync(filePath);
  
  if (exists) {
    const stats = fs.statSync(filePath);
    console.log(`✅ ${file} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${file} - 文件缺失`);
    allFilesExist = false;
  }
});

// 检查图标文件
console.log('\n🎨 检查图标文件:');
requiredIcons.forEach(iconPath => {
  const filePath = path.join(distDir, iconPath);
  const exists = fs.existsSync(filePath);

  if (exists) {
    const stats = fs.statSync(filePath);
    console.log(`✅ ${iconPath} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${iconPath} - 文件缺失`);
    allFilesExist = false;
  }
});

// 检查assets目录
const assetsDir = path.join(distDir, 'assets');
if (fs.existsSync(assetsDir)) {
  const assetFiles = fs.readdirSync(assetsDir);
  console.log(`\n📦 Assets目录: ${assetFiles.length} 个文件`);
  assetFiles.forEach(file => {
    const stats = fs.statSync(path.join(assetsDir, file));
    console.log(`  - ${file} (${stats.size} bytes)`);
  });
} else {
  console.log('\n❌ Assets目录不存在');
  allFilesExist = false;
}

// 验证workspace-placeholder.js内容
const jsFile = path.join(distDir, 'workspace-placeholder.js');
if (fs.existsSync(jsFile)) {
  const content = fs.readFileSync(jsFile, 'utf8');
  if (content.includes('deleteSelectedTabs') && content.includes('showDeleteConfirmation')) {
    console.log('✅ workspace-placeholder.js 包含必要的删除功能');
  } else {
    console.log('❌ workspace-placeholder.js 缺少删除功能');
    allFilesExist = false;
  }
}

console.log('\n' + '='.repeat(50));
if (allFilesExist) {
  console.log('🎉 构建验证通过！所有文件都存在且正确。');
  process.exit(0);
} else {
  console.log('💥 构建验证失败！请检查上述错误。');
  process.exit(1);
}
